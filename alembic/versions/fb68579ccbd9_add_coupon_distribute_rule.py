"""add_coupon_distribute_rule

Revision ID: fb68579ccbd9
Revises: c2ef52c8fdbb
Create Date: 2025-09-05 22:17:15.529251

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fb68579ccbd9'
down_revision: Union[str, None] = 'c2ef52c8fdbb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('coupon_distribution_rules',
    sa.Column('id', sa.Integer(), nullable=False, comment='规则ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='规则名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='规则描述'),
    sa.Column('type', sa.Enum('USER_FEATURE', 'USER_BEHAVIOR', 'USER_FEATURE_AND_BEHAVIOR', name='distributionruletype'), nullable=False, comment='规则类型'),
    sa.Column('user_feature_function', sa.Text(), nullable=True, comment='用户特征函数'),
    sa.Column('user_behavior_types', sa.JSON(), nullable=True, comment='用户行为类型'),
    sa.Column('distribution_content', sa.JSON(), nullable=False, comment='发放内容'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='distributionrulestatus'), nullable=False, comment='规则状态'),
    sa.Column('execution_time', sa.String(length=255), nullable=True, comment='执行时间(cron表达式)'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_coupon_distribution_rules'))
    )
    op.create_index(op.f('ix_coupon_distribution_rules_id'), 'coupon_distribution_rules', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_coupon_distribution_rules_id'), table_name='coupon_distribution_rules')
    op.drop_table('coupon_distribution_rules')
    # ### end Alembic commands ###
