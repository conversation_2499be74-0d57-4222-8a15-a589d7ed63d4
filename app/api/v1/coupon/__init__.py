from fastapi import APIRouter

from .coupon import router as coupon_router
from .coupon_batch import router as coupon_batch_router
from .coupon_usage_record import router as coupon_usage_record_router
from .coupon_distribution_rule import router as coupon_distribution_rule_router

router = APIRouter()

# 包含所有子路由
router.include_router(coupon_router, tags=["coupons"])
router.include_router(coupon_batch_router, tags=["coupon-batches"])
router.include_router(coupon_usage_record_router, tags=["coupon-usage-records"])
router.include_router(coupon_distribution_rule_router, prefix="/distribution-rules", tags=["coupon-distribution-rules"])
