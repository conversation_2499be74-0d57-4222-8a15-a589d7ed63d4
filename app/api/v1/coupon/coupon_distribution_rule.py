from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.coupon import coupon_distribution_rule_dao
from app.models.coupon import DistributionRuleType, DistributionRuleStatus
from app.schemas.common import CommonResponse
from app.schemas.coupon import (
    CouponDistributionRuleCreate, CouponDistributionRuleUpdate, CouponDistributionRuleResponse,
    CouponDistributionRuleSearch, CouponDistributionRuleListResponse, CouponDistributionRuleListData,
    CouponDistributionRuleStatusUpdateRequest
)

router = APIRouter()


@router.post("/", response_model=CommonResponse)
def create_distribution_rule(
    rule: CouponDistributionRuleCreate,
    db: Session = Depends(get_db)
) -> Any:
    """
    创建优惠券发放规则
    """
    try:
        # 验证规则类型和必填字段
        if rule.type == DistributionRuleType.USER_FEATURE and not rule.user_feature_function:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户特征类型规则必须指定用户特征函数"
            )
        
        if rule.type == DistributionRuleType.USER_BEHAVIOR and not rule.user_behavior_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户行为类型规则必须指定用户行为类型"
            )
        
        if rule.type == DistributionRuleType.USER_FEATURE_AND_BEHAVIOR:
            if not rule.user_feature_function or not rule.user_behavior_types:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户特征与行为类型规则必须同时指定用户特征函数和用户行为类型"
                )
        
        # 转换为字典格式
        rule_data = rule.model_dump()
        
        # 处理用户行为类型列表
        if rule_data.get('user_behavior_types'):
            rule_data['user_behavior_types'] = [action.value for action in rule.user_behavior_types]
        
        # 处理发放内容
        if rule_data.get('distribution_content'):
            rule_data['distribution_content'] = [item.model_dump() for item in rule.distribution_content]
        
        db_rule = coupon_distribution_rule_dao.create(db, rule_data)
        
        return CommonResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleResponse.model_validate(db_rule)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建发放规则失败: {str(e)}"
        )


@router.get("/{rule_id}", response_model=CommonResponse)
def get_distribution_rule(
    rule_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """
    获取优惠券发放规则详情
    """
    db_rule = coupon_distribution_rule_dao.get(db, rule_id)
    if not db_rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="发放规则不存在"
        )
    
    return CommonResponse(
        code=200,
        message="success",
        data=CouponDistributionRuleResponse.model_validate(db_rule)
    )


@router.put("/{rule_id}", response_model=CommonResponse)
def update_distribution_rule(
    rule_id: int,
    rule: CouponDistributionRuleUpdate,
    db: Session = Depends(get_db)
) -> Any:
    """
    更新优惠券发放规则
    """
    try:
        # 检查规则是否存在
        db_rule = coupon_distribution_rule_dao.get(db, rule_id)
        if not db_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="发放规则不存在"
            )
        
        # 转换为字典格式
        rule_data = rule.model_dump(exclude_unset=True)
        
        # 处理用户行为类型列表
        if 'user_behavior_types' in rule_data and rule_data['user_behavior_types']:
            rule_data['user_behavior_types'] = [action.value for action in rule.user_behavior_types]
        
        # 处理发放内容
        if 'distribution_content' in rule_data and rule_data['distribution_content']:
            rule_data['distribution_content'] = [item.model_dump() for item in rule.distribution_content]
        
        updated_rule = coupon_distribution_rule_dao.update(db, rule_id, **rule_data)
        
        return CommonResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleResponse.model_validate(updated_rule)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新发放规则失败: {str(e)}"
        )


@router.delete("/{rule_id}", response_model=CommonResponse)
def delete_distribution_rule(
    rule_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """
    删除优惠券发放规则
    """
    try:
        # 检查规则是否存在
        db_rule = coupon_distribution_rule_dao.get(db, rule_id)
        if not db_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="发放规则不存在"
            )
        
        success = coupon_distribution_rule_dao.delete(db, rule_id)
        
        return CommonResponse(
            code=200,
            message="success",
            data=success
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除发放规则失败: {str(e)}"
        )


@router.get("/", response_model=CouponDistributionRuleListResponse)
def list_distribution_rules(
    keyword: str = Query(None, description="关键词搜索"),
    rule_type: DistributionRuleType = Query(None, description="规则类型"),
    rule_status: DistributionRuleStatus = Query(None, description="规则状态"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="每页数量"),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取优惠券发放规则列表
    """
    try:
        search_params = CouponDistributionRuleSearch(
            keyword=keyword,
            type=rule_type,
            status=rule_status
        )
        
        result = coupon_distribution_rule_dao.search(
            db, 
            keyword=search_params.keyword,
            rule_type=search_params.type,
            status=search_params.status,
            skip=skip, 
            limit=limit
        )
        
        # 转换为响应模型
        rule_responses = [
            CouponDistributionRuleResponse.model_validate(rule) 
            for rule in result["list"]
        ]
        
        return CouponDistributionRuleListResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleListData(
                total=result["total"],
                list=rule_responses
            )
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取发放规则列表失败: {str(e)}"
        )


@router.patch("/{rule_id}/status", response_model=CommonResponse)
def update_distribution_rule_status(
    rule_id: int,
    status_request: CouponDistributionRuleStatusUpdateRequest,
    db: Session = Depends(get_db)
) -> Any:
    """
    更新优惠券发放规则状态
    """
    try:
        # 检查规则是否存在
        db_rule = coupon_distribution_rule_dao.get(db, rule_id)
        if not db_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="发放规则不存在"
            )
        
        updated_rule = coupon_distribution_rule_dao.update_status(db, rule_id, status_request.status)
        
        return CommonResponse(
            code=200,
            message="success",
            data=CouponDistributionRuleResponse.model_validate(updated_rule)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新发放规则状态失败: {str(e)}"
        )
