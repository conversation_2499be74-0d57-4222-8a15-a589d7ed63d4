from app.events import UserEvent
from app.events.handlers import event_handler, logger
from app.events.models import UserEventAction


@event_handler(UserEvent)
async def handle_user_events(event: UserEvent):
    """处理用户相关事件。"""
    # 打印 UserEvent 的所有信息
    logger.debug("=" * 60)
    logger.debug("  UserEvent 详细信息")
    logger.debug("=" * 60)
    logger.debug(f"  事件ID: {event.id}")
    logger.debug(f"  时间戳: {event.timestamp}")
    logger.debug(f"  来源: {event.source}")
    logger.debug(f"️  事件类型: {event.event_type}")
    logger.debug(f"  操作: {event.action}")
    logger.debug(f"  用户ID: {event.user_id}")
    logger.debug(f"  用户类型: {event.user_type}")
    logger.debug(f"  用户名: {event.username}")
    logger.debug(f"  附加数据: {event.additional_data}")
    logger.debug("-" * 60)
    logger.debug(f"  完整事件数据 (JSON): {event.dict()}")
    logger.debug("=" * 60)

    # 根据不同的操作类型进行处理
    if event.action == UserEventAction.CREATED:
        logger.info(f"处理用户创建: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.REGISTERED:
        logger.info(f"处理用户注册: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.LOGIN:
        logger.info(f"处理用户登录: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.LOGOUT:
        logger.info(f"处理用户登出: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.UPDATED:
        logger.info(f"处理用户信息更新: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.PASSWORD_CHANGED:
        logger.info(f"处理用户密码更改: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.DELETED:
        logger.info(f"处理用户删除: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.VIEWED:
        logger.debug(f"处理用户查看事件: 用户ID {event.user_id}, 用户名: {event.username}")

    elif event.action == UserEventAction.SHARED:
        logger.info(f"处理用户分享事件: 用户ID {event.user_id}, 用户名: {event.username}")

    else:
        logger.warning(f"未处理的用户事件操作: {event.action}")

    logger.info(f"  UserEvent 处理完成: {event.id}")
    logger.info("=" * 60)
